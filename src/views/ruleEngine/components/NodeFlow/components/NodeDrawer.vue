<template>
  <el-drawer
    :title="(nodeData.properties && nodeData.properties.name) || '节点配置'"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="80%"
    @close="handleClose"
  >
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="基础信息" name="basic">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="节点类型">
            <el-tag
              :type="
                getNodeTypeColor(
                  nodeData.properties ? nodeData.properties.nodeType : ''
                )
              "
            >
              {{
                nodeData.properties ? nodeData.properties.nodeType : "基础节点"
              }}
            </el-tag>
          </el-form-item>

          <el-form-item label="节点名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入节点名称"
              @input="handleNameChange"
            />
          </el-form-item>

          <component
            v-if="currentFormComponent"
            :is="currentFormComponent"
            :formData="formData"
            @update:formData="updateFormData"
            ref="nodeFormComponent"
            :chainId="chainId"
          />

          <el-form-item label="描述">
            <el-input
              v-model="formData.description"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入节点描述信息"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane v-if="isDebug" label="调试信息" name="debug">
        <div v-loading="loading">
          <div v-if="!debugList || debugList.length === 0" class="empty-debug">
            <el-empty description="暂无调试信息" />
          </div>
          <debug-table v-else :debug-list="debugList" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { queryNodeDataList } from "@/api/ruleEngine/index";
import DebugTable from "./DebugTable.vue";
export default {
  name: "NodeDrawer",
  components: {
    DebugTable,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    nodeData: {
      type: Object,
      default: () => ({}),
    },
    isDebug: {
      type: Boolean,
      default: false,
    },
    chainId: {
      type: String,
      default: "",
    },
    chainInstanceId: {
      type: String,
      default: "",
    },
    chainName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeTab: "basic",
      debugList: null,
      formData: {
        name: "",
        description: "",
        timeout: 3,
      },
      rules: {
        name: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
      },
      loading: false,
      currentFormComponent: null,
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    formComponentName() {
      const nodeType = this.nodeData.properties?.nodeType;
      return nodeType;
    },
  },
  watch: {
    formComponentName: {
      immediate: true,
      handler(nodeType) {
        if (nodeType) {
          const componentName = `${nodeType}Form`;
          import(`./nodeForms/${componentName}.vue`)
            .then((module) => {
              this.currentFormComponent = module.default;
            })
            .catch((error) => {
              console.error(
                `Failed to load form component for ${nodeType}:`,
                error
              );
              this.currentFormComponent = null;
            });
        } else {
          this.currentFormComponent = null;
        }
      },
    },
    value(newVal) {
      if (newVal) {
        if (this.nodeData?.type === "start-node") {
          this.$message.warning("开始节点不可编辑");
          this.$emit("close");
          return;
        }
        console.log(this.nodeData, "nodeData");
        // 使用深拷贝初始化表单数据
        this.formData = {
          description: this.nodeData.description || "",
          name: this.getNodeName(),
          ...JSON.parse(JSON.stringify(this.nodeData.properties || {})), // 深拷贝properties
        };
      } else {
        this.activeTab = "basic";
      }
    },
  },
  methods: {
    // 获取节点名称
    getNodeName() {
      return this.nodeData.properties?.name || "";
    },
    // 获取节点类型颜色
    getNodeTypeColor(nodeType) {
      const colorMap = {
        IF: "primary",
        THEN: "success",
        SWITCH: "warning",
        FOR: "info",
        WHILE: "info",
        TIMEOUT: "danger",
      };
      return colorMap[nodeType] || "";
    },
    updateFormData(newData) {
      this.formData = { ...this.formData, ...newData };
    },
    handleError(error) {
      this.$message.error(error);
    },
    generateRandomName() {
      // 生成6位随机字符串（数字和字母组合）
      const randomStr = String.fromCharCode(
        65 + Math.floor(Math.random() * 26)
      );
      return `${randomStr}`;
    },
    async handleSubmit() {
      try {
        // 验证整个表单
        await this.$refs.formRef.validate();

        // 获取表单数据并深拷贝
        let completeFormData = {
          ...JSON.parse(JSON.stringify(this.nodeData.properties || {})), // 深拷贝原有属性
          ...JSON.parse(JSON.stringify(this.formData)), // 深拷贝新的属性
          nodeType: this.nodeData.properties?.nodeType,
          name: this.formData.name,
          description: this.formData.description,
          saved: true, // 标记为已保存
        };
        if (completeFormData.paramsValue) {
          completeFormData.paramsValue["nodeName"] = this.formData.name;
        }

        // 保存节点数据
        this.$emit("save", completeFormData);
        this.handleClose();
      } catch (error) {
        console.warn("表单验证失败");
      }
    },
    handleClose() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
      this.$emit("close");
    },
    // 输入时自动过滤特殊字符
    handleNameChange() {
      // 只保留中文、英文、数字和下划线
      this.formData.name = this.formData.name.replace(
        /[^a-zA-Z0-9\u4e00-\u9fa5_]/g,
        ""
      );
    },
    handleTabChange(tab) {
      if (tab.name === "debug" && this.isDebug) {
        // 获取单个节点调试信息
        this.getSingleNodeDebugInfo();
      }
    },
    async getSingleNodeDebugInfo() {
      try {
        if (!this.chainInstanceId) {
          this.$message.error("请先选择实例");
          return;
        }
        this.loading = true;
        const res = await queryNodeDataList({
          chainId: this.chainId,
          chainInstanceId: this.chainInstanceId,
          nodeId: this.nodeData.properties?.scriptName,
        });
        if (res && res.success) {
          this.debugList = res.data || [];
        } else {
          this.debugList = [];
        }
      } catch (error) {
        this.$message.error("获取调试信息失败");
        this.debugList = [];
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.empty-debug {
  padding: 40px 0;
  text-align: center;
}
</style>
