<!-- 告警详情页面 -->
<template>
  <div class="alarm-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack">
          返回
        </el-button>
        <h2 class="page-title">告警内容</h2>
      </div>
      <div class="header-right">
        <el-button
          type="danger"
          @click="handleRelease"
          v-if="alarmData.alarmStatus == 1 || alarmData.alarmStatus == 2"
          v-has-permi="['ruleEngine:resultSearch:lift']"
          >解除</el-button
        >
      </div>
    </div>

    <!-- Tab 切换区域 -->
    <el-card class="tab-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="预警概览" name="overview">
          <OverviewTab
            ref="overviewTab"
            :alarmCode="alarmCode"
            :alarmData="alarmData"
            :shouldLoadData="true"
            @dataLoaded="handleOverviewDataLoaded"
          />
        </el-tab-pane>
        <el-tab-pane label="规则逻辑" name="ruleLogic">
          <RuleLogicTab
            ref="ruleLogicTab"
            :alarmCode="alarmCode"
            :shouldLoadData="activeTab === 'ruleLogic'"
          />
        </el-tab-pane>
        <el-tab-pane label="上下文数据" name="contextData">
          <ContextDataTab
            ref="contextDataTab"
            :alarmCode="alarmCode"
            :shouldLoadData="activeTab === 'contextData'"
          />
        </el-tab-pane>
        <el-tab-pane label="处理记录" name="processRecord">
          <ProcessRecordTab
            ref="processRecordTab"
            :alarmCode="alarmCode"
            :shouldLoadData="activeTab === 'processRecord'"
          />
        </el-tab-pane>
        <el-tab-pane label="执行日志" name="executionLog">
          <ExecutionLogTab
            ref="executionLogTab"
            :alarmCode="alarmCode"
            :shouldLoadData="activeTab === 'executionLog'"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 解除弹窗 -->
    <BaseFormModal
      ref="releaseModal"
      modalTitle="解除预警"
      :config="releaseFormConfig"
      @modalConfirm="handleReleaseConfirm"
      modalWidth="50%"
    />
  </div>
</template>

<script>
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import OverviewTab from "./components/OverviewTab.vue";
import RuleLogicTab from "./components/RuleLogicTab.vue";
import ContextDataTab from "./components/ContextDataTab.vue";
import ProcessRecordTab from "./components/ProcessRecordTab.vue";
import ExecutionLogTab from "./components/ExecutionLogTab.vue";
import api from "@/api/ruleEngine/resultSearch.js";
import { initParams } from "@/utils/buse";

export default {
  name: "AlarmDetail",
  components: {
    BaseFormModal,
    OverviewTab,
    RuleLogicTab,
    ContextDataTab,
    ProcessRecordTab,
    ExecutionLogTab,
  },
  data() {
    return {
      statusOptions: [],
      riskLevelOptions: [],
      activeTab: "overview",
      alarmCode: "",
      alarmData: {},
      // 解除弹窗表单配置
    };
  },
  computed: {
    releaseFormConfig() {
      return [
        {
          field: "relieveStatus",
          title: "解除进度",
          element: "el-select",
          props: {
            //这里是通过接口异步获取，也可以直接在这写死
            options: this.statusOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            filterable: true,
          },
          rules: [
            {
              required: true,
              message: "请选择解除进度",
            },
          ],
        },
        {
          field: "relieveReason",
          title: "解除原因",
          props: {
            type: "textarea",
          },
          attrs: {
            placeholder: "500个字符以内",
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
          },
        },
      ];
    },
  },
  created() {
    this.alarmCode = this.$route.query.id;
    if (!this.alarmCode) {
      this.$message.error("缺少告警ID参数");
      this.goBack();
      return;
    }
    this.loadDictionaries();
    this.loadAlarmBasicInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载数据字典
    async loadDictionaries() {
      try {
        // 获取告警处理状态字典
        const statusRes = await this.getDicts("alarm_handle_status");
        this.statusOptions = statusRes.data || [];

        // 获取风险等级字典
        const riskRes = await this.getDicts("alarm_risk_level");
        this.riskLevelOptions = riskRes.data || [];
      } catch (error) {
        console.error("加载数据字典失败:", error);
        this.statusOptions = [];
        this.riskLevelOptions = [];
      }
    },

    // 加载告警基本信息
    async loadAlarmBasicInfo() {
      try {
        // 调用YApi接口获取告警详情
        const res = await api.getDetail({ alarmCode: this.alarmCode });

        if (res.success && res.data) {
          this.alarmData = {
            alarmCode: res.data.alarmCode || "",
            alarmContent: res.data.nodeName || "",
            riskLevel: res.data.riskLevelName || "",
            alarmTime: res.data.alarmTime || "",
            ruleName: res.data.ruleName || "",
            ruleDesc: res.data.chainDesc || "",
            ruleVersion: res.data.version || "",
            nodeName: res.data.nodeName || "",
            triggerValue: res.data.triggerValue || "",
            threshold: res.data.thresholdValue || "",
            triggerValueAndThreshold: res.data.triggerValueAndThreshold || "",
            alarmStatus: res.data.alarmStatus || "",
            chainId: res.data.chainId || "",
          };
        } else {
          throw new Error("接口返回数据格式错误");
        }
      } catch (error) {
        this.$message.error("加载告警信息失败");
        console.error(error);
      }
    },

    // Tab 切换事件
    handleTabClick(tab) {
      console.log("切换到tab:", tab.name);

      // 根据切换的Tab调用对应的数据加载方法
      this.$nextTick(() => {
        // this.refreshCurrentTabData(tab.name);
      });
    },

    // 刷新当前Tab的数据
    refreshCurrentTabData(tabName) {
      try {
        switch (tabName) {
          case "overview":
            if (
              this.$refs.overviewTab &&
              this.$refs.overviewTab.loadTrendData
            ) {
              this.$refs.overviewTab.loadTrendData();
              this.loadAlarmBasicInfo();
            }
            break;
          case "ruleLogic":
            if (this.$refs.ruleLogicTab && this.$refs.ruleLogicTab.refresh) {
              this.$refs.ruleLogicTab.refresh();
            }
            break;
          case "contextData":
            if (
              this.$refs.contextDataTab &&
              this.$refs.contextDataTab.refresh
            ) {
              this.$refs.contextDataTab.refresh();
            }
            break;
          case "processRecord":
            if (
              this.$refs.processRecordTab &&
              this.$refs.processRecordTab.refresh
            ) {
              this.$refs.processRecordTab.refresh();
            }
            break;
          case "executionLog":
            if (
              this.$refs.executionLogTab &&
              this.$refs.executionLogTab.refresh
            ) {
              this.$refs.executionLogTab.refresh();
            }
            break;
          default:
            console.warn("未知的Tab名称:", tabName);
        }
      } catch (error) {
        console.error("刷新Tab数据失败:", error);
      }
    },

    // 概览数据加载完成回调
    handleOverviewDataLoaded(data) {
      // 可以在这里处理概览数据加载完成后的逻辑
      console.log("概览数据加载完成:", data);
    },

    // 解除按钮点击
    handleRelease() {
      this.$refs.releaseModal.open({
        alarmCode: this.alarmCode,
        ...initParams(this.releaseFormConfig),
      });
    },

    // 解除确认
    async handleReleaseConfirm(formData) {
      try {
        // 调用YApi解除预警接口
        const params = {
          alarmCodeList: [this.alarmCode],
          relieveStatus: formData.relieveStatus,
          relieveReason: formData.relieveReason,
        };

        const res = await api.relieve(params);

        if (res.success || res.code === "10000") {
          this.$message.success("解除成功");
          // 重新加载告警信息以更新状态
          // await this.loadAlarmBasicInfo();

          // 刷新当前激活Tab的数据
          this.$nextTick(() => {
            this.refreshCurrentTabData(this.activeTab);
          });

          // 如果解除成功，可以选择返回列表页或停留在当前页
          // this.goBack();
        } else {
          this.$message.error(res.message || "解除失败");
        }
      } catch (error) {
        this.$message.error("解除失败");
        console.error(error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-detail-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      .page-title {
        margin: 0 0 0 10px;
        font-size: 20px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .tab-container {
    min-height: 600px;

    /deep/ .el-tabs__content {
      padding-top: 20px;
    }
  }
}
</style>
