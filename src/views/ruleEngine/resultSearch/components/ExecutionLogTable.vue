<!-- 执行日志表格组件 -->
<template>
  <div class="execution-log-table">
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column
        prop="nodeInstanceId"
        label="执行结果编号"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="dataValue"
        label="执行数据"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        prop="dataType"
        label="数据类型"
        min-width="100"
        show-overflow-tooltip
      />
      <el-table-column
        prop="nodeName"
        label="节点名称"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="status"
        label="执行状态"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === 'success' ? 'success' : 'danger'"
            size="small"
            v-if="scope.row.statusText"
          >
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="operator"
        label="操作人"
        width="120"
        show-overflow-tooltip
      /> -->
      <el-table-column
        prop="startTime"
        label="开始时间"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column
        prop="endTime"
        label="结束时间"
        width="160"
        show-overflow-tooltip
      />
      <el-table-column
        prop="costTime"
        label="消耗时间/ms"
        width="120"
        align="right"
      />
      <!-- <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleViewDetail(scope.row)"
          >
            明细
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页组件 -->
    <!-- <div class="pagination-wrapper" v-if="showPagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div> -->
  </div>
</template>

<script>
import api from "@/api/ruleEngine/resultSearch.js";

export default {
  name: "ExecutionLogTable",
  props: {
    alarmCode: {
      type: String,
      required: true,
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 表格高度
    tableHeight: {
      type: [String, Number],
      default: 400,
    },
    shouldLoadData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  watch: {
    shouldLoadData: {
      handler(newVal) {
        console.log("shouldLoadData", newVal);
        if (newVal) {
          this.loadExecutionLogs();
        }
      },
      immediate: false,
    },
  },
  created() {
    // 页面初始化时不自动加载数据，等待shouldLoadData变化或手动调用
  },
  methods: {
    // 加载执行日志数据
    async loadExecutionLogs() {
      this.loading = true;
      try {
        // 调用YApi执行日志接口
        const params = {
          alarmCode: this.alarmCode,
          // pageNum: this.pagination.currentPage,
          // pageSize: this.pagination.pageSize,
        };

        const res = await api.getExecutionLog(params);

        if (res.success && res.data && Array.isArray(res.data)) {
          // 处理接口返回的数据格式
          this.tableData = res.data?.map((item) => ({
            ...item,
            status: item.successFlag == 1 ? "success" : "error",
            statusText:
              item.successFlag == 1
                ? "成功"
                : item.successFlag == 0
                ? "失败"
                : "",
          }));
          this.pagination.total = res.data.length;
        } else {
          // 使用模拟数据作为fallback
          // this.loadMockData();
        }
      } catch (error) {
        console.error("加载执行日志失败:", error);
        // this.loadMockData();
      } finally {
        this.loading = false;
      }
    },

    // 加载模拟数据
    loadMockData() {
      // 暂时使用模拟数据
      this.tableData = [
        {
          nodeInstanceId: "exec20240815001",
          dataValue: "{'温度': 85.5, '状态': 'active'}",
          status: "success",
          statusText: "正常",
          operator: "系统自动",
          startTime: "2024-08-15 10:30:15",
          endTime: "2024-08-15 10:30:16",
          costTime: 37,
        },
        {
          nodeInstanceId: "exec20240815002",
          dataValue: "{'温度': 92.1, '状态': 'active'}",
          status: "success",
          statusText: "正常",
          operator: "系统自动",
          startTime: "2024-08-15 10:31:15",
          endTime: "2024-08-15 10:31:16",
          costTime: 47,
        },
        {
          nodeInstanceId: "exec20240815003",
          dataValue: "{'温度': 78.3, '状态': 'inactive'}",
          status: "success",
          statusText: "正常",
          operator: "系统自动",
          startTime: "2024-08-15 10:32:15",
          endTime: "2024-08-15 10:32:16",
          costTime: 39,
        },
        {
          nodeInstanceId: "exec20240815004",
          dataValue: "{'温度': 105.2, '状态': 'error'}",
          status: "error",
          statusText: "异常",
          operator: "系统自动",
          startTime: "2024-08-15 10:33:15",
          endTime: "2024-08-15 10:33:17",
          costTime: 28,
        },
        {
          nodeInstanceId: "exec20240815005",
          dataValue: "{'温度': 88.7, '状态': 'active'}",
          status: "error",
          statusText: "异常",
          operator: "系统自动",
          startTime: "2024-08-15 10:34:15",
          endTime: "2024-08-15 10:34:18",
          costTime: 59,
        },
        {
          nodeInstanceId: "exec20240815006",
          dataValue: "{'温度': 76.4, '状态': 'active'}",
          status: "success",
          statusText: "正常",
          operator: "系统自动",
          startTime: "2024-08-15 10:35:15",
          endTime: "2024-08-15 10:35:16",
          costTime: 33,
        },
      ];
      this.pagination.total = 6;
    },

    // 查看明细
    handleViewDetail(row) {
      // 暂时不实现，按需求要求
      this.$message.info("明细功能将在后续开发中实现");
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadExecutionLogs();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadExecutionLogs();
    },

    // 刷新数据
    refresh() {
      this.loadExecutionLogs();
    },
  },
};
</script>

<style lang="less" scoped>
.execution-log-table {
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
