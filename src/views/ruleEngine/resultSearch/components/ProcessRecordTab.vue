<!-- 处理记录Tab -->
<template>
  <div class="process-record-tab">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理记录</span>
      </div>

      <div v-loading="loading" class="content-area">
        <Timeline
          :list="processRecords"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="createTime"
          operateDetailTitle="remark"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import { queryLog } from "@/api/common.js";

export default {
  name: "ProcessRecordTab",
  components: {
    Timeline,
  },
  props: {
    alarmCode: {
      type: String,
      required: true,
    },
    shouldLoadData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      processRecords: [],
    };
  },
  watch: {
    shouldLoadData: {
      handler(newVal) {
        if (newVal) {
          this.loadProcessRecords();
        }
      },
      immediate: false,
    },
  },
  created() {
    // 页面初始化时不自动加载数据，等待shouldLoadData变化或手动调用
  },
  methods: {
    // 加载处理记录数据
    async loadProcessRecords() {
      this.loading = true;
      try {
        // 调用API获取处理记录数据
        const res = await queryLog({ businessId: this.alarmCode });
        this.processRecords = res.data;

        this.loading = false;
      } catch (error) {
        this.loading = false;
        this.$message.error("加载处理记录失败");
        console.error(error);
      }
    },

    // 刷新数据方法
    refresh() {
      this.loadProcessRecords();
    },
  },
};
</script>

<style lang="less" scoped>
.process-record-tab {
  padding: 20px;

  .card-title-wrap {
    display: flex;
    align-items: center;

    .card-title-line {
      width: 4px;
      height: 16px;
      background: #00b099;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .content-area {
    min-height: 300px;
  }
}
</style>
