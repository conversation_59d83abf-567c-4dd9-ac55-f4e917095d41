<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="showCreateModal"
          v-has-permi="['ruleEngine:list:add']"
        >
          新增规则链
        </el-button>
      </template>

      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ruleEngine:list:add']"
          >
            导出
          </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            :loading="loading"
          >
            查询
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click.stop="handleReset"
            :loading="loading"
          >
            重置
          </el-button>
        </div>
      </template>

      <!-- 规则链名称插槽 -->
      <template #chainName="{ row }">
        <el-link
          @click="handleConfig(row)"
          :disabled="!checkPermission(['ruleEngine:list:editProcess'])"
        >
          {{ row.chainName }}
        </el-link>
      </template>

      <!-- 状态插槽 -->
      <template #enable="{ row }">
        <el-switch
          v-model="row.enable"
          :disabled="true"
          active-text="已启用"
          inactive-text="未启用"
        />
      </template>

      <!-- 筛选状态插槽 -->
      <template #enableFilter>
        <el-select
          v-model="params.enable"
          placeholder="请选择启用状态"
          clearable
        >
          <el-option label="已启用" :value="true" />
          <el-option label="未启用" :value="false" />
        </el-select>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import {
  getRuleChainList,
  updateRuleChainEnable,
  addRuleChain,
  updateRuleChain,
  getRuleChainDetail,
  deleteRuleChain,
} from "@/api/ruleEngine/index.js";
import { initParams } from "@/utils/buse.js";
import checkPermission from "@/utils/permission";

export default {
  name: "RuleChainList",
  data() {
    return {
      loading: false,
      tableData: [],
      params: {},
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
    };
  },
  computed: {
    // 表格列配置
    tableColumn() {
      return [
        { title: "序号", type: "seq", align: "center", width: 60 },
        {
          title: "规则名称",
          field: "chainName",
          align: "center",
          minWidth: "120px",
          slots: { default: "chainName" },
        },
        {
          title: "规则描述",
          field: "chainDesc",
          align: "center",
          maxWidth: "200px",
        },
        {
          title: "规则链版本",
          field: "version",
          align: "center",
        },
        {
          title: "更新时间",
          field: "updateTime",
          align: "center",
        },
        {
          title: "创建人",
          field: "createUser",
          align: "center",
        },
        {
          title: "状态",
          field: "enable",
          slots: { default: "enable" },
          align: "center",
          width: "180px",
        },
      ];
    },

    // 筛选配置
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        config: [
          {
            field: "chainName",
            title: "规则名称",
            defaultValue: undefined,
          },
          {
            field: "enable",
            title: "状态",
            element: "slot",
            slotName: "enableFilter",
            defaultValue: undefined,
          },
        ],
        params: this.params,
      };
    },

    // 表格属性
    tableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      };
    },

    // 弹窗配置
    modalConfig() {
      return {
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        addBtn: false,
        editBtn: checkPermission(["ruleEngine:list:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["ruleEngine:list:delete"]),
        viewBtn: false,
        modalWidth: "600px",
        formConfig: [
          {
            field: "chainName",
            title: "规则链名称",
            rules: [
              { required: true, message: "请输入规则链名称", trigger: "blur" },
              { max: 32, message: "规则链名称最多32个字符", trigger: "blur" },
            ],
            props: {
              disabled: true,
            },
          },
          {
            field: "chainDesc",
            title: "规则链描述",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 3,
            },
            rules: [
              { max: 64, message: "规则链描述最多64个字符", trigger: "blur" },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "启用",
            typeName: "enable",
            event: (row) => {
              return this.handleChainStatus(row, true);
            },
            condition: (row) => {
              return !row.enable && checkPermission(["ruleEngine:list:enable"]);
            },
          },
          {
            title: "禁用",
            typeName: "disable",
            event: (row) => {
              return this.handleChainStatus(row, false);
            },
            condition: (row) => {
              return row.enable && checkPermission(["ruleEngine:list:disable"]);
            },
          },
          {
            title: "复制",
            typeName: "copy",
            event: (row) => {
              return this.handleCopy(row);
            },
            condition: () => {
              return checkPermission(["ruleEngine:list:copy"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },
  methods: {
    checkPermission,
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 调用API获取规则链列表
        const res = await getRuleChainList({
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });

        this.tablePage.total = res.total;
        this.tableData = res?.data || [];
      } catch (error) {
        this.$message.error("加载数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 新增规则链 - 直接跳转到详情页
    showCreateModal() {
      localStorage.setItem("isDebug", "false");
      this.$router.push({
        name: "rule-engine-details",
      });
    },

    // 编辑规则链
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", row);
    },

    // 配置规则链 - 跳转到详情页
    handleConfig(row) {
      localStorage.setItem("isDebug", "false");
      this.$router.push({
        name: "rule-engine-details",
        query: {
          chainId: row.chainId,
        },
      });
    },

    // 启用/禁用规则链
    async handleChainStatus(row, enable) {
      const action = enable ? "启用" : "禁用";
      try {
        await this.$confirm(
          `确定要${action}规则链"${row.chainName}"吗？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await updateRuleChainEnable({
          id: row.chainId,
          enable,
        });
        this.$message.success(`${action}成功`);
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`${action}失败`);
        }
      }
    },

    // 复制规则链
    async handleCopy(row) {
      try {
        await this.$confirm(
          `确定要复制规则链"${row.chainName}"吗？`,
          "复制规则链",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        const detailRes = await getRuleChainDetail({
          id: row.chainId,
        });

        const copyData = {
          ...detailRes.data,
          chainId: "",
          chainName: `${detailRes.data.chainName}-副本`,
          enable: false,
        };

        const addRes = await addRuleChain(copyData);

        if (addRes && addRes.data.chainId) {
          this.$message.success("复制成功");
          this.loadData();

          // 弹框提示是否跳转详情
          try {
            await this.$confirm("是否跳转到详情页进行编辑?", "复制成功", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "info",
            });

            localStorage.setItem("isDebug", "false");
            this.$router.push({
              name: "rule-engine-details",
              query: {
                chainId: addRes.data.chainId,
              },
            });
          } catch (error) {
            // 用户选择不跳转，不做任何操作
          }
        } else {
          this.$message.error("复制失败");
        }
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("复制失败");
        }
      }
    },

    // 删除规则链
    async deleteRowHandler(row) {
      if (row.enable) {
        this.$message.warning("启用状态的规则链不能删除，请先禁用");
        return;
      }

      try {
        await this.$confirm(`确定要删除规则链"${row.chainName}"吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        await deleteRuleChain({
          id: row.chainId,
        });
        this.$message.success("删除成功");
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("删除失败");
        }
      }
    },

    // 弹窗确认 - 编辑规则链
    async modalConfirmHandler(formData) {
      try {
        await updateRuleChain({
          chainId: formData.chainId,
          chainName: formData.chainName,
          chainDesc: formData.chainDesc,
        });
        this.$message.success("更新成功");
        this.loadData();
      } catch (error) {
        // 错误已在拦截器中处理
      }
    },

    // 查询
    handleQuery() {
      this.tablePage.currentPage = 1;

      this.loadData();
    },

    // 重置
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    // 导出
    handleExport() {
      this.$message.info("导出功能开发中");
    },
  },
};
</script>

<style lang="less" scoped>
.btn-wrap {
  display: flex;
  gap: 8px;
}

.link-text {
  color: #029c7c;
  text-decoration: none;

  &:hover {
    color: #029c7c;
    text-decoration: underline;
  }
}
</style>
