<template>
  <el-dialog
    title="选择调拨物料"
    :visible.sync="visible"
    width="90%"
    :before-close="handleClose"
    append-to-body
    class="material-select-dialog"
  >
    <div class="dialog-content">
      <!-- 全选操作 -->
      <div class="select-all-section">
        <el-checkbox
          v-model="selectAllFiltered"
          @change="handleSelectAllFiltered"
        >
          全选所有筛选结果
        </el-checkbox>
        <!-- <el-button type="danger" size="small" @click="handleClearSelected">
          删除
        </el-button> -->
      </div>

      <!-- 使用BuseCrud组件 -->
      <BuseCrud
        ref="materialCrud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :pagerProps="pagerProps"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :tableProps="tableProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      />
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api/materielManage/allocate.js";

export default {
  name: "MaterialSelectDialog",
  data() {
    return {
      visible: false,
      loading: false,
      selectAllFiltered: false,

      filterParams: {
        materialName: "",
        materialModel: "",
        repositoryName: "",
        materialFactory: "",
        materialNo: "",
      },

      tableData: [],
      selectedMaterials: [],
      warehouseOptions: [],

      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },

      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        rowConfig: {
          keyField: "quantityId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },

      modalConfig: {
        showModal: false,
      },

      filterOptions: {
        showCount: 3,
        layout: "right",
        inline: true,
        labelWidth: "100px",
        config: [
          {
            field: "materialName",
            title: "物料名称",
            element: "el-autocomplete",
            props: {
              placeholder: "请选择或输入物料名称",
              fetchSuggestions: this.queryMaterialNames,
            },
          },
          {
            field: "materialModel",
            title: "规格型号",
            element: "el-autocomplete",
            props: {
              placeholder: "请选择或输入规格型号",
              fetchSuggestions: this.queryMaterialModels,
            },
          },
          {
            field: "repositoryName",
            title: "出库仓库",
            element: "el-select",
            props: {
              placeholder: "请选择出库仓库",
              options: [],
            },
          },
          {
            field: "materialFactory",
            title: "生产厂家",
            element: "el-input",
            props: {
              placeholder: "请输入生产厂家",
            },
          },
          {
            field: "materialNo",
            title: "物料编码",
            element: "el-autocomplete",
            props: {
              placeholder: "请选择或输入物料编码",
              fetchSuggestions: this.queryMaterialCodes,
            },
          },
        ],
        params: this.filterParams,
      },

      tableColumn: [
        {
          type: "checkbox",
          width: 50,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          formatter: ({ cellValue }) => {
            return cellValue ? `¥${cellValue}` : "-";
          },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
        },
        {
          field: "repositoryName",
          title: "当前所在仓库",
          width: 120,
        },
        {
          field: "repositoryCategory",
          title: "仓库类别",
          width: 120,
        },
        {
          field: "usableStatus",
          title: "可用状态",
          width: 100,
        },
      ],
    };
  },
  created() {
    this.loadWarehouseOptions();
  },
  methods: {
    // 打开弹窗
    open() {
      this.visible = true;
      this.loadData();
    },

    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        const options =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];

        this.warehouseOptions = options;

        // 更新filterOptions中的仓库选项
        const warehouseFilter = this.filterOptions.config.find(
          (item) => item.field === "repositoryName"
        );
        if (warehouseFilter) {
          warehouseFilter.props.options = options;
        }
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        const res = await api.queryMaterialByPage({
          ...this.filterParams,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } catch (error) {
        this.$message.error(`加载数据失败：${error.message || "网络错误"}`);
      } finally {
        this.loading = false;
      }
    },

    // 查询物料名称建议
    async queryMaterialNames(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      try {
        const res = await api.queryMaterialByPage({
          materialName: queryString,
          pageNum: 1,
          pageSize: 10,
        });
        const suggestions =
          res.data?.map((item) => ({
            value: item.materialName,
          })) || [];
        cb(suggestions);
      } catch (error) {
        cb([]);
      }
    },

    // 查询规格型号建议
    async queryMaterialModels(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      try {
        const res = await api.queryMaterialByPage({
          materialModel: queryString,
          pageNum: 1,
          pageSize: 10,
        });
        const suggestions =
          res.data?.map((item) => ({
            value: item.materialModel,
          })) || [];
        cb(suggestions);
      } catch (error) {
        cb([]);
      }
    },

    // 查询物料编码建议
    async queryMaterialCodes(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      try {
        const res = await api.queryMaterialByPage({
          materialNo: queryString,
          pageNum: 1,
          pageSize: 10,
        });
        const suggestions =
          res.data?.map((item) => ({
            value: item.materialNo,
          })) || [];
        cb(suggestions);
      } catch (error) {
        cb([]);
      }
    },

    // 全选筛选结果
    handleSelectAllFiltered(value) {
      this.$nextTick(() => {
        if (value) {
          this.$refs.materialCrud.$refs.xTable.setAllCheckboxRow(true);
        } else {
          this.$refs.materialCrud.$refs.xTable.clearCheckboxRow();
        }
      });
    },

    // 删除选中
    handleClearSelected() {
      this.$refs.materialCrud.$refs.xTable.clearCheckboxRow();
      this.selectAllFiltered = false;
    },

    // 确认选择
    handleConfirm() {
      const selected = this.$refs.materialCrud.$refs.xTable.getCheckboxRecords(
        true
      );
      if (!selected?.length) {
        this.$message.warning("请选择要调拨的物料");
        return;
      }
      this.$emit("confirm", selected);
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false;
      this.filterParams = {
        materialName: "",
        materialModel: "",
        repositoryName: "",
        materialFactory: "",
        materialNo: "",
      };
      this.selectAllFiltered = false;
      this.tablePage.currentPage = 1;
      this.$refs.materialCrud?.$refs.xTable?.clearCheckboxRow();
    },
  },
};
</script>

<style lang="less" scoped>
.material-select-dialog {
  .dialog-content {
    height: 600px;
    display: flex;
    flex-direction: column;
  }

  .filter-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background: #fafafa;
  }

  .filter-form {
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .select-all-section {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .dialog-footer {
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
