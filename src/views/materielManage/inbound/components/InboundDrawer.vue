<template>
  <el-drawer
    :title="drawerTitle"
    :visible.sync="visible"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
    class="inbound-drawer"
  >
    <div class="drawer-content">
      <!-- 统计信息 -->
      <div class="statistics-bar">
        <span
          >已录入{{ materialCount }}类物料，共计{{ totalQuantity }}个物料</span
        >
      </div>

      <!-- 操作按钮 -->
      <div class="operation-bar">
        <el-button
          type="primary"
          size="small"
          @click="handleAdd"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          icon="el-icon-delete"
        >
          删除
        </el-button>
      </div>

      <!-- 表格 -->
      <StationConfigTable
        ref="configTable"
        :columns="tableColumns"
        v-model="tableData"
        :showAddBtn="false"
      />

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import api from "@/api/materielManage/inbound.js";
import materialApi from "@/api/materielManage/index.js";

export default {
  name: "InboundDrawer",
  components: {
    StationConfigTable,
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      editMode: false,
      editData: null,
      tableData: [],

      // 字典选项
      warehouseOptions: [],
      inboundCategoryOptions: [],
      usableStatusOptions: [],
      materialOptions: [],
    };
  },
  computed: {
    drawerTitle() {
      return this.editMode ? "编辑入库物料明细" : "在线录入物料明细";
    },

    // 统计物料类别数量
    materialCount() {
      return this.tableData.length;
    },

    // 统计物料总数量
    totalQuantity() {
      return this.tableData.reduce((total, item) => {
        return total + (parseInt(item.boundNum) || 0);
      }, 0);
    },

    tableColumns() {
      return [
        {
          type: "checkbox",
          width: 50,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.materialOptions,
            optionLabel: "materialNo",
            optionValue: "materialNo",
            placeholder: "请选择物料编码",
            filterable: true,
            remote: true,
            remoteMethod: this.queryMaterials,
          },
          on: {
            change: this.handleMaterialChange,
          },
          rules: [
            { required: true, message: "请选择物料编码", trigger: "change" },
          ],
        },
        {
          field: "inRepositoryName",
          title: "入库仓库",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.warehouseOptions,
            optionLabel: "label",
            optionValue: "value",
            placeholder: "请选择入库仓库",
          },
          rules: [
            { required: true, message: "请选择入库仓库", trigger: "change" },
          ],
        },
        {
          field: "inCategory",
          title: "入库类别",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.inboundCategoryOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            placeholder: "请选择入库类别",
          },
          rules: [
            { required: true, message: "请选择入库类别", trigger: "change" },
          ],
        },
        {
          field: "purchaseNum",
          title: "采购单号",
          width: 120,
          isEdit: true,
          element: "el-input",
          props: {
            placeholder: "请输入采购单号",
          },
        },
        {
          field: "boundNum",
          title: "入库数量",
          width: 100,
          isEdit: true,
          element: "el-input-number",
          props: {
            min: 1,
            max: 999999,
            controlsPosition: "right",
            placeholder: "请输入入库数量",
          },
          rules: [
            { required: true, message: "请输入入库数量", trigger: "blur" },
            {
              type: "number",
              min: 1,
              max: 999999,
              message: "入库数量范围为1-999999",
              trigger: "blur",
            },
          ],
        },
        {
          field: "usableStatus",
          title: "可用状态",
          width: 100,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.usableStatusOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            placeholder: "请选择可用状态",
          },
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
          isEdit: false,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
          isEdit: false,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
          isEdit: false,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          isEdit: false,
          formatter: ({ cellValue }) => {
            return cellValue ? `¥${cellValue}` : "-";
          },
        },
      ];
    },
  },
  created() {
    this.initDictData();
  },
  methods: {
    // 初始化字典数据
    async initDictData() {
      try {
        const [inboundCategoryRes, usableStatusRes] = await Promise.all([
          this.getDicts("wl_inbound_type"),
          this.getDicts("wl_usable_status"),
        ]);

        this.inboundCategoryOptions = inboundCategoryRes.data;
        this.usableStatusOptions = usableStatusRes.data;

        // 加载仓库选项
        this.loadWarehouseOptions();
      } catch (error) {
        console.error("初始化字典数据失败:", error);
      }
    },

    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },

    // 查询物料列表
    async queryMaterials(query) {
      if (!query) {
        this.materialOptions = [];
        return;
      }

      try {
        const res = await materialApi.getTableData({
          materialNo: query,
          pageNum: 1,
          pageSize: 20,
          status: 0, // 只查询启用状态的物料
        });
        this.materialOptions = res.data || [];
      } catch (error) {
        console.error("查询物料失败:", error);
        this.materialOptions = [];
      }
    },

    // 物料编码变化处理
    async handleMaterialChange(value, item, row) {
      if (!value) return;

      try {
        const res = await materialApi.getByMaterialNo(value);
        if (res.success && res.data) {
          const material = res.data;
          // 自动填充物料信息
          this.$set(row, "materialName", material.materialName);
          this.$set(row, "materialModel", material.materialModel);
          this.$set(row, "unit", material.unit);
          this.$set(row, "unitPrice", material.unitPrice);
          this.$set(row, "materialFactory", material.materialFactory);
        }
      } catch (error) {
        console.error("获取物料信息失败:", error);
        this.$message.error("获取物料信息失败");
      }
    },

    // 打开抽屉
    open(editData = null) {
      this.visible = true;
      this.editMode = !!editData;
      this.editData = editData;

      if (editData) {
        // 编辑模式，加载现有数据
        this.tableData = [{ ...editData }];
      } else {
        // 新增模式，初始化一行空数据
        this.tableData = [this.getEmptyRow()];
      }
    },

    // 获取空行数据
    getEmptyRow() {
      return {
        materialNo: "",
        inRepositoryName: "",
        inCategory: "",
        purchaseNum: "",
        boundNum: 1,
        usableStatus: "",
        materialName: "",
        materialModel: "",
        unit: "",
        unitPrice: "",
        materialFactory: "",
      };
    },

    // 新增行
    handleAdd() {
      this.tableData.push(this.getEmptyRow());
    },

    // 批量删除
    handleBatchDelete() {
      const selected = this.$refs.configTable.$refs.xTable.getCheckboxRecords(
        true
      );
      if (!selected?.length) {
        this.$message.warning("请勾选要删除的行");
        return;
      }

      this.tableData = this.tableData.filter(
        (item) => !selected.includes(item)
      );
    },

    // 确认提交
    async handleConfirm() {
      try {
        // 验证表单
        const valid = await this.$refs.configTable.validate();
        if (!valid) {
          this.$message.error("请完善表单信息");
          return;
        }

        // 获取选中的数据
        const selected = this.$refs.configTable.$refs.xTable.getCheckboxRecords(
          true
        );
        if (!selected?.length) {
          this.$message.warning("请勾选要提交的数据");
          return;
        }

        this.confirmLoading = true;

        if (this.editMode) {
          // 编辑模式
          await api.update({
            ...this.editData,
            ...selected[0],
          });
          this.$message.success("修改成功");
        } else {
          // 新增模式
          await api.batchInsert({
            dtoList: selected,
          });
          this.$message.success("入库成功");
        }

        this.$emit("success");
        this.handleClose();
      } catch (error) {
        this.$message.error(`操作失败：${error.message || "网络错误"}`);
      } finally {
        this.confirmLoading = false;
      }
    },

    // 关闭抽屉
    handleClose() {
      this.visible = false;
      this.editMode = false;
      this.editData = null;
      this.tableData = [];
      this.confirmLoading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.inbound-drawer {
  .drawer-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .statistics-bar {
    background: #f5f7fa;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #606266;
  }

  .operation-bar {
    margin-bottom: 15px;
    text-align: right;
  }

  .footer-buttons {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
