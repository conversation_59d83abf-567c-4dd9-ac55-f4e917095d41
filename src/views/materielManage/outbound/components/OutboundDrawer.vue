<template>
  <el-drawer
    :title="drawerTitle"
    :visible.sync="visible"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
    class="outbound-drawer"
  >
    <div class="drawer-content">
      <!-- 表单区域 -->
      <div class="form-section">
        <DynamicForm
          ref="dynamicForm"
          :config="formConfig"
          :params="formData"
          :defaultColSpan="12"
          labelPosition="right"
          labelWidth="120px"
        >
          <template #attachments>
            <fileUpload4
              v-model="formData.attachments"
              :accept="'.jpg,.png,.doc,.docx,.xls,.xlsx,.pdf'"
              :showPaste="true"
              :textTip="
                '支持粘贴图片上传，上传格式为jpg/png/doc/docx/xls/xlsx/pdf'
              "
            />
          </template>
        </DynamicForm>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-bar">
        <span
          >已选择{{ materialCount }}类物料，共计{{ totalQuantity }}个物料</span
        >
      </div>

      <!-- 操作按钮 -->
      <div class="operation-bar">
        <el-button
          type="primary"
          size="small"
          @click="handleSelectMaterial"
          icon="el-icon-plus"
        >
          请选择出库物料
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          icon="el-icon-delete"
        >
          删除
        </el-button>
        <el-checkbox v-model="selectAll" @change="handleSelectAllChange">
          全选筛选结果
        </el-checkbox>
      </div>

      <!-- 表格 -->
      <StationConfigTable
        ref="configTable"
        :columns="tableColumns"
        v-model="tableData"
        :showAddBtn="false"
      />

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          暂存
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定出库
        </el-button>
      </div>
    </div>

    <!-- 选择物料弹窗 -->
    <MaterialSelectDialog
      ref="materialSelectDialog"
      @confirm="handleMaterialSelected"
    />
  </el-drawer>
</template>

<script>
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import MaterialSelectDialog from "./MaterialSelectDialog.vue";
import fileUpload4 from "@/components/Upload/fileUpload4.vue";
import api from "@/api/materielManage/outbound.js";

import { queryCityTree } from "@/api/common.js";

export default {
  name: "OutboundDrawer",
  components: {
    StationConfigTable,
    MaterialSelectDialog,
    fileUpload4,
  },
  data() {
    return {
      visible: false,
      saveLoading: false,
      confirmLoading: false,
      editMode: false,
      editData: null,
      tableData: [],
      selectAll: true,

      // 表单数据
      formData: {
        outCategory: "",
        outFlow: "",
        flowRepositoryName: "",
        receiver: "",
        receiveProvince: "",
        receiveCity: "",
        trackingNum: "",
        receiveAddress: "",
        remark: "",
        attachments: [],
      },

      // 字典选项
      outboundCategoryOptions: [],
      outFlowOptions: [],
      warehouseOptions: [],
      regionData: [],
    };
  },
  computed: {
    drawerTitle() {
      return this.editMode ? "编辑出库物料明细" : "批量出库物料明细";
    },

    // 统计物料类别数量
    materialCount() {
      return this.getSelectedData().length;
    },

    // 统计物料总数量
    totalQuantity() {
      return this.getSelectedData().reduce((total, item) => {
        return total + (parseInt(item.boundNum) || 0);
      }, 0);
    },

    formConfig() {
      return [
        {
          field: "outCategory",
          element: "el-select",
          title: "出库类别",
          props: {
            filterable: true,
            placeholder: "请选择出库类别",
            options: this.outboundCategoryOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
          },
          rules: [
            { required: true, message: "请选择出库类别", trigger: "change" },
          ],
        },
        {
          field: "outFlow",
          element: "el-select",
          title: "出库流向",
          props: {
            filterable: true,
            placeholder: "请选择出库流向",
            options: this.outFlowOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
          },
        },
        {
          field: "flowRepositoryName",
          element: "el-select",
          title: "流向仓库",
          props: {
            filterable: true,
            placeholder: "请选择流向仓库",
            options: this.warehouseOptions,
          },
        },
        {
          field: "receiver",
          element: "el-input",
          title: "收件人",
        },
        {
          field: "region",
          element: "el-cascader",
          title: "收件省市",
          props: {
            options: this.regionData,
            props: {
              checkStrictly: false,
              multiple: false,
              value: "areaCode",
              label: "areaName",
            },
          },
        },
        {
          field: "trackingNum",
          element: "el-input",
          title: "快递单号",
        },
        {
          field: "receiveAddress",
          element: "el-input",
          title: "收件地址",
        },
        {
          field: "remark",
          element: "el-input",
          title: "备注",
          attrs: {
            type: "textarea",
            rows: 5,
            maxlength: 10000,
            showWordLimit: true,
            placeholder: "请输入备注，10000个字符以内",
          },
          colProps: {
            span: 24,
          },
        },
        {
          field: "attachments",
          element: "slot",
          title: "附件",
          slotName: "attachments",
          colProps: {
            span: 24,
          },
        },
      ];
    },

    tableColumns() {
      return [
        {
          type: "checkbox",
          width: 50,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
          isEdit: false,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
          isEdit: false,
        },
        {
          field: "boundNum",
          title: "出库数量",
          width: 100,
          isEdit: true,
          element: "el-input-number",
          props: {
            min: 1,
            max: 999999,
            controlsPosition: "right",
            placeholder: "请输入出库数量",
          },
          rules: [
            { required: true, message: "请输入出库数量", trigger: "blur" },
            {
              type: "number",
              min: 1,
              max: 999999,
              message: "出库数量范围为1-999999",
              trigger: "blur",
            },
          ],
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
          isEdit: false,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
          isEdit: false,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          isEdit: false,
          formatter: ({ cellValue }) => {
            return cellValue ? `¥${cellValue}` : "-";
          },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
          isEdit: false,
        },
        {
          field: "usableStatus",
          title: "可用状态",
          width: 100,
          isEdit: false,
        },
        {
          field: "repositoryName",
          title: "当前所在仓库",
          width: 120,
          isEdit: false,
        },
        {
          field: "repositoryCategory",
          title: "仓库类别",
          width: 120,
          isEdit: false,
        },
      ];
    },
  },
  created() {
    this.initDictData();
  },
  methods: {
    // 初始化字典数据
    async initDictData() {
      try {
        const [outboundCategoryRes, outFlowRes] = await Promise.all([
          this.getDicts("wl_outbound_type"),
          this.getDicts("wl_out_flow"),
        ]);

        this.outboundCategoryOptions = outboundCategoryRes.data;
        this.outFlowOptions = outFlowRes.data;

        // 加载仓库选项
        this.loadWarehouseOptions();
        // 加载省市数据
        this.getCityRegionData();
      } catch (error) {
        console.error("初始化字典数据失败:", error);
      }
    },

    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },

    // 获取省市数据
    async getCityRegionData() {
      try {
        const res = await queryCityTree({});
        this.regionData = this.cleanTree(res.data);
      } catch (error) {
        console.error("获取省市数据失败:", error);
      }
    },

    // 清理树形数据
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = this.cleanTree(newItem.children);
        } else {
          delete newItem.children;
        }
        return newItem;
      });
    },

    // 打开抽屉
    async open(editData = null) {
      this.visible = true;
      this.editMode = !!editData;
      this.editData = editData;

      if (editData) {
        // 编辑模式，加载该出库单号下的所有物料记录
        this.formData = { ...editData };
        await this.loadOutboundDetails(editData.operNo);
      } else {
        // 新增模式，初始化空数据
        this.formData = {
          outCategory: "",
          outFlow: "",
          flowRepositoryName: "",
          receiver: "",
          receiveProvince: "",
          receiveCity: "",
          trackingNum: "",
          receiveAddress: "",
          remark: "",
          attachments: [],
        };
        this.tableData = [];
      }
      this.selectAll = true;
    },

    // 加载出库单详情
    async loadOutboundDetails(operNo) {
      try {
        const res = await api.getOutboundDetailsByOperNo(operNo);
        this.tableData = res.data || [];
      } catch (error) {
        this.$message.error(
          `加载出库单详情失败：${error.message || "网络错误"}`
        );
        this.tableData = [];
      }
    },

    // 选择物料
    handleSelectMaterial() {
      this.$refs.materialSelectDialog.open();
    },

    // 物料选择确认
    handleMaterialSelected(selectedMaterials) {
      // 将选中的物料添加到表格中，设置默认出库数量为1
      const newMaterials = selectedMaterials.map((item) => ({
        ...item,
        boundNum: 1,
      }));
      this.tableData = [...this.tableData, ...newMaterials];

      // 默认全选新添加的物料
      this.$nextTick(() => {
        this.handleSelectAllChange(true);
      });
    },

    // 批量删除
    handleBatchDelete() {
      const selected = this.$refs.configTable.$refs.xTable.getCheckboxRecords(
        true
      );
      if (!selected?.length) {
        this.$message.warning("请勾选要删除的行");
        return;
      }

      this.tableData = this.tableData.filter(
        (item) => !selected.includes(item)
      );
    },

    // 全选/取消全选
    handleSelectAllChange(value) {
      this.$nextTick(() => {
        if (value) {
          this.$refs.configTable.$refs.xTable.setAllCheckboxRow(true);
        } else {
          this.$refs.configTable.$refs.xTable.clearCheckboxRow();
        }
      });
    },

    // 获取选中的数据
    getSelectedData() {
      if (!this.$refs.configTable?.$refs.xTable) {
        return [];
      }
      const selected = this.$refs.configTable.$refs.xTable.getCheckboxRecords(
        true
      );
      return selected || [];
    },

    // 暂存
    async handleSave() {
      try {
        // 验证表单
        try {
          await this.$refs.dynamicForm.validate();
        } catch (error) {
          this.$message.error("请完善表单信息");
          return;
        }

        // 验证表格
        const tableValid = await this.$refs.configTable.validate();
        if (!tableValid) {
          this.$message.error("请完善表格信息");
          return;
        }

        // 获取选中的数据
        const selected = this.getSelectedData();
        if (!selected?.length) {
          this.$message.warning("请勾选要提交的数据");
          return;
        }

        this.saveLoading = true;

        const submitData = {
          ...this.formData,
          operStatus: "1", // 暂存状态
          dtoList: selected.map((item) => ({
            quantityId: item.quantityId,
            num: item.boundNum,
          })),
        };

        if (this.editMode) {
          await api.update({
            ...this.editData,
            ...submitData,
          });
          this.$message.success("暂存成功");
        } else {
          await api.batchInsert(submitData);
          this.$message.success("暂存成功");
        }

        this.$emit("success");
        this.handleClose();
      } catch (error) {
        this.$message.error(`暂存失败：${error.message || "网络错误"}`);
      } finally {
        this.saveLoading = false;
      }
    },

    // 确定出库
    async handleConfirm() {
      try {
        // 验证表单
        try {
          await this.$refs.dynamicForm.validate();
        } catch (error) {
          this.$message.error("请完善表单信息");
          return;
        }

        // 验证表格
        const tableValid = await this.$refs.configTable.validate();
        if (!tableValid) {
          this.$message.error("请完善表格信息");
          return;
        }

        // 获取选中的数据
        const selected = this.getSelectedData();
        if (!selected?.length) {
          this.$message.warning("请勾选要提交的数据");
          return;
        }

        this.confirmLoading = true;

        const submitData = {
          ...this.formData,
          operStatus: "3", // 已出库待收货状态
          dtoList: selected.map((item) => ({
            quantityId: item.quantityId,
            num: item.boundNum,
          })),
        };

        if (this.editMode) {
          await api.update({
            ...this.editData,
            ...submitData,
          });
          this.$message.success("出库成功");
        } else {
          await api.batchInsert(submitData);
          this.$message.success("出库成功");
        }

        this.$emit("success");
        this.handleClose();
      } catch (error) {
        this.$message.error(`出库失败：${error.message || "网络错误"}`);
      } finally {
        this.confirmLoading = false;
      }
    },

    // 关闭抽屉
    handleClose() {
      this.visible = false;
      this.editMode = false;
      this.editData = null;
      this.tableData = [];
      this.formData = {
        outCategory: "",
        outFlow: "",
        flowRepositoryName: "",
        receiver: "",
        receiveProvince: "",
        receiveCity: "",
        trackingNum: "",
        receiveAddress: "",
        remark: "",
        attachments: [],
      };
      this.saveLoading = false;
      this.confirmLoading = false;
      this.selectAll = true;
    },
  },
};
</script>

<style lang="less" scoped>
.outbound-drawer {
  .drawer-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .form-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background: #fafafa;
  }

  .statistics-bar {
    background: #f5f7fa;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #606266;
  }

  .operation-bar {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .footer-buttons {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
