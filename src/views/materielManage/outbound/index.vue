<!-- 物料出库管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchOutbound"
          v-has-permi="['materielManage:outbound:add']"
          >批量出库</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:outbound:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
    </BuseCrud>

    <!-- 批量出库抽屉 -->
    <OutboundDrawer ref="outboundDrawer" @success="handleQuery" />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage/outbound.js";
import exportMixin from "@/mixin/export.js";
import OutboundDrawer from "./components/OutboundDrawer.vue";
import { listUser } from "@/api/common.js";

export default {
  name: "OutboundPage",
  components: { OutboundDrawer },
  mixins: [exportMixin],
  data() {
    return {
      // 字典选项
      statusOptions: [],
      warehouseOptions: [],
      outboundCategoryOptions: [],
      outFlowOptions: [],
      operatorOptions: [],

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "boundId",
          isCurrent: true,
        },
        spanMethod: this.mergeRowMethod,
      },
      tableData: [],
      tableColumn: [
        {
          field: "operNo",
          title: "出库单号",
          width: 150,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
        },
        {
          field: "boundNum",
          title: "出库数量",
          width: 100,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          formatter: ({ cellValue }) => {
            return cellValue ? `¥${cellValue}` : "-";
          },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
        },
        {
          field: "operStatus",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            const statusMap = {
              "1": "暂存",
              "3": "已出库待收货",
              "4": "已出库已收货",
              "5": "待审核",
              "6": "审核通过",
              "7": "审核不通过",
              "9": "已确认收货",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        {
          field: "outRepositoryName",
          title: "出库仓库",
          width: 120,
        },
        {
          field: "outCategory",
          title: "出库类别",
          width: 120,
        },
        {
          field: "outFlow",
          title: "出库流向",
          width: 120,
        },
        {
          field: "flowRepositoryName",
          title: "流向的仓库",
          width: 120,
        },
        {
          field: "operName",
          title: "出库人",
          width: 100,
        },
        {
          field: "operTimeStr",
          title: "出库时间",
          width: 150,
        },
        {
          field: "receiver",
          title: "收件人",
          width: 100,
        },
        {
          field: "receiveAddress",
          title: "收件地址",
          width: 200,
        },
        {
          field: "trackingNum",
          title: "快递单号/工单单号",
          width: 150,
        },
        {
          field: "confirmer",
          title: "确认收货人",
          width: 100,
        },
        {
          field: "confirmTimeStr",
          title: "确认收货时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "出库单号",
            props: {
              placeholder: "请输入出库单号",
            },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: {
              placeholder: "请输入物料名称",
            },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: {
              placeholder: "请输入生产厂家",
            },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择状态",
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "出库时间",
            props: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始时间",
              endPlaceholder: "结束时间",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
          },
          {
            field: "outRepositoryName",
            element: "el-select",
            title: "出库仓库",
            props: {
              options: this.warehouseOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择出库仓库",
            },
          },
          {
            field: "outCategory",
            element: "el-select",
            title: "出库类别",
            props: {
              options: this.outboundCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择出库类别",
            },
          },
          {
            field: "outFlow",
            element: "el-select",
            title: "出库流向",
            props: {
              options: this.outFlowOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择出库流向",
            },
          },
          {
            field: "operName",
            element: "el-select",
            title: "出库人",
            props: {
              options: this.operatorOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择出库人",
            },
          },
          {
            field: "confirmTimeRange",
            element: "el-date-picker",
            title: "确认收货时间",
            props: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始时间",
              endPlaceholder: "结束时间",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
          },
          {
            field: "trackingNum",
            element: "el-input",
            title: "快递单号",
            props: {
              placeholder: "请输入快递单号",
            },
          },
          {
            field: "receiver",
            element: "el-input",
            title: "收件人",
            props: {
              placeholder: "请输入收件人",
            },
          },
          {
            field: "flowRepositoryName",
            element: "el-select",
            title: "流向的仓库",
            props: {
              options: this.warehouseOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择流向的仓库",
            },
          },
          {
            field: "confirmer",
            element: "el-select",
            title: "收货人",
            props: {
              options: this.operatorOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择收货人",
            },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: {
              placeholder: "请输入物料编码",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增出库",
        editTitle: "编辑出库",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:outbound:edit"]),
        delBtn: checkPermission(["materielManage:outbound:delete"]),
        menu: true,
        menuWidth: 200,
        menuFixed: "right",
        formConfig: [],
        customOperationTypes: [
          {
            title: "确认收货",
            typeName: "confirm",
            event: (row) => {
              return this.confirmReceive(row);
            },
            condition: (row) => {
              return (
                row.operStatus === "3" &&
                checkPermission(["materielManage:outbound:confirm"])
              );
            },
          },
        ],
      };
    },
  },
  created() {
    this.initDictData();
    this.loadData();
  },
  methods: {
    checkPermission,

    // 初始化字典数据
    async initDictData() {
      try {
        // 获取状态字典
        this.statusOptions = [
          { label: "暂存", value: "1" },
          { label: "已出库待收货", value: "3" },
          { label: "已出库已收货", value: "4" },
          { label: "待审核", value: "5" },
          { label: "审核通过", value: "6" },
          { label: "审核不通过", value: "7" },
          { label: "已确认收货", value: "9" },
        ];

        // 获取字典数据
        const [outboundCategoryRes, outFlowRes] = await Promise.all([
          this.getDicts("wl_outbound_type"),
          this.getDicts("wl_out_flow"),
        ]);

        this.outboundCategoryOptions = outboundCategoryRes.data;
        this.outFlowOptions = outFlowRes.data;

        // 获取仓库列表
        this.loadWarehouseOptions();
        // 获取操作人员列表
        this.getListUser();
      } catch (error) {
        console.error("初始化字典数据失败:", error);
      }
    },

    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.operatorOptions = data;
    },

    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 处理时间范围参数
        const params = { ...this.params };
        if (params.operTimeRange && params.operTimeRange.length === 2) {
          params.operTimeStart = params.operTimeRange[0];
          params.operTimeEnd = params.operTimeRange[1];
          delete params.operTimeRange;
        }
        if (params.confirmTimeRange && params.confirmTimeRange.length === 2) {
          params.confirmTimeStart = params.confirmTimeRange[0];
          params.confirmTimeEnd = params.confirmTimeRange[1];
          delete params.confirmTimeRange;
        }

        const res = await api.getTableData({
          ...params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } catch (error) {
        this.$message.error(`加载数据失败：${error.message || "网络错误"}`);
      } finally {
        this.loading = false;
      }
    },

    // 行合并方法（相同出库单号的行合并指定列）
    mergeRowMethod({ row, column, rowIndex }) {
      console.log("col", column);
      const mergeFields = [
        "operNo",
        "operStatus",
        "outRepositoryName",
        "outCategory",
        "outFlow",
        "flowRepositoryName",
        "operName",
        "operTimeStr",
        "receiver",
        "receiveAddress",
        "trackingNum",
        "confirmer",
        "confirmTimeStr",
      ];

      if (mergeFields.includes(column.field) || column.title === "操作") {
        const currentOperNo = row.operNo;
        let rowspan = 1;
        let colspan = 1;

        // 向上查找相同出库单号的行
        for (let i = rowIndex - 1; i >= 0; i--) {
          if (this.tableData[i].operNo === currentOperNo) {
            return { rowspan: 0, colspan: 0 };
          } else {
            break;
          }
        }

        // 向下查找相同出库单号的行
        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].operNo === currentOperNo) {
            rowspan++;
          } else {
            break;
          }
        }

        return { rowspan, colspan };
      }
    },

    // 批量出库
    handleBatchOutbound() {
      this.$refs.outboundDrawer.open();
    },

    // 编辑行
    rowEdit(row) {
      this.$refs.outboundDrawer.open(row);
    },

    // 删除行
    async deleteRowHandler(row) {
      try {
        await this.$confirm(
          `确认删除出库单号"${row.operNo}"的物料"${row.materialName}"吗？\n\n删除后无法恢复！`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }
        );

        await api.remove(row);
        this.loadData();
        this.$message.success("删除成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`删除失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 确认收货
    async confirmReceive(row) {
      try {
        await this.$confirm(
          `确认收货出库单号"${row.operNo}"的物料"${row.materialName}"吗？`,
          "确认收货",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await api.confirm(row);
        this.loadData();
        this.$message.success("确认收货成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`确认收货失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 模态框确认处理
    async modalConfirmHandler() {
      // 这里暂时不处理，因为使用抽屉组件
    },

    // 查询
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      this.params = {};
      this.handleQuery();
    },

    // 导出
    handleExport() {
      const params = {
        ...this.params,
      };
      // 处理时间范围参数
      if (params.operTimeRange && params.operTimeRange.length === 2) {
        params.operTimeStart = params.operTimeRange[0];
        params.operTimeEnd = params.operTimeRange[1];
        delete params.operTimeRange;
      }
      if (params.confirmTimeRange && params.confirmTimeRange.length === 2) {
        params.confirmTimeStart = params.confirmTimeRange[0];
        params.confirmTimeEnd = params.confirmTimeRange[1];
        delete params.confirmTimeRange;
      }
      this.handleCommonExport(api.export, params);
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  padding: 20px;
  background: #fff;
  height: 100%;
}
.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}
</style>
