<!-- 仓库管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="rowAdd"
          v-has-permi="['materielManage:warehouse:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:warehouse:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
      <template #dept="{item, params}">
        <treeselect
          v-model="params.orgNo"
          :options="deptOptions"
          placeholder="请选择组织"
          :default-expand-level="1"
          :normalizer="normalizer"
        />
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline :list="recordList"></Timeline>
      </template>
      <template #statusChange="{ row }">
        <el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['materielManage:warehouse:status'])"
        >
        </el-switch>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.5"
      maxSizeText="500m"
    >
    </BatchUpload>
    <DetailDrawer ref="detailDrawer"></DetailDrawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage/warehouse.js";
import Timeline from "@/components/Timeline/index.vue";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { initParams } from "@/utils/buse.js";
import DetailDrawer from "./components/detailDrawer.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listAllUser, queryCityTree, listDept } from "@/api/common.js";
import { regionData } from "element-china-area-data";

import Treeselect from "@riophae/vue-treeselect";
export default {
  name: "materialPage",
  components: { Timeline, BatchUpload, DetailDrawer, Treeselect },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/export/report/importStaffInfo",
        url: "/charging-maintenance-ui/static/人员档案批量导入模板.xlsx",
        extraData: {},
      },
      materielTypeOptions: [],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "materialCode",
          title: "仓库名称",
          width: 120,
        },
        {
          field: "materialName",
          title: "仓库类别",
          width: 150,
        },
        {
          field: "materialType",
          title: "所属组织",
          width: 120,
        },
        {
          field: "modelNumber",
          title: "所在城市",
          width: 120,
        },
        {
          field: "manufacturer",
          title: "详细地址",
          width: 150,
        },
        {
          field: "applicableModels",
          title: "仓储负责人",
          width: 150,
        },
        {
          field: "unit",
          title: "联系电话",
          width: 120,
        },
        {
          field: "count",
          title: "库存数量（个）",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleDetail(row),
                  }}
                >
                  {row.count || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
        {
          field: "createName",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateName",
          title: "修改人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      recordList: [],
      unitOptions: [],
      userOptions: [],
      regionData: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "materialName",
            element: "el-input",
            title: "仓库名称",
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: 1 },
                { label: "启用", value: 0 },
              ],
              placeholder: "请选择状态",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增",
        editTitle: "编辑",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:warehouse:edit"]),
        delBtn: checkPermission(["materielManage:warehouse:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        formConfig: [
          {
            field: "materialType",
            title: "仓库名称",
            element: "el-input",
            attrs: {
              maxlength: 100,
            },
            rules: [{ required: true, message: "请输入仓库名称" }],
          },
          {
            field: "materialCode",
            title: "仓库类别",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryWarehouseType");
              },
            },
            attrs: {
              maxlength: 100,
            },
            rules: [{ required: true, message: "请输入仓库类别" }],
          },
          {
            field: "orgNo",
            title: "所属组织",
            element: "slot",
            slotName: "dept",
            rules: [{ required: true, message: "请选择所属组织" }],
          },
          {
            field: "region",
            title: "所在城市",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              clearable: true,
              filterable: true,
              props: {
                checkStrictly: true,
                multiple: false,
                // value: "areaCode",
                // label: "areaName",
              },
              options: regionData?.map((x) => {
                return { ...x, disabled: true };
              }),
            },
          },
          {
            field: "userIds",
            element: "el-select",
            title: "负责人",
            props: {
              options: this.userOptions,
              multiple: true,
              filterable: true,
              placeholder: "可多选，选择后将影响仓库确认收货人的数据范畴",
            },
            rules: [{ required: true, message: "请选择通知人" }],
          },
          {
            field: "materialType",
            title: "联系电话",
            element: "el-input",
            attrs: {
              maxlength: 100,
            },
            rules: [
              { required: true, message: "请输入联系电话" },
              {
                pattern: /^(1[3-9]\d{9})$|^(\d{3,4}-\d{7,8})$/,
                message: "请输入正确的格式",
              },
            ],
          },
        ],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.getDicts("materiel_type").then((response) => {
      this.materielTypeOptions = response.data;
    });
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
    this.getTreeselect();
    this.listAllUser();
    this.getCityRegionData();
    this.loadData();
  },
  methods: {
    checkPermission,
    handleDetail(row) {
      this.$refs.detailDrawer.open(row);
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        name: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    async loadData() {
      this.loading = true;
      try {
        const res = await api.getTableData({
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data;
        this.tablePage.total = res.total;
      } finally {
        this.loading = false;
      }
    },
    async handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      this.$confirm(`是否确认${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          api
            .changeStatus({ urgencyId: row.urgencyId, status: row.status })
            .then((res) => {
              if (res.code == "10000") {
                this.msgSuccess(text + "成功");
                this.loadData();
              } else {
                row.status = row.status == "1" ? "0" : "1";
              }
            })
            .catch(() => {
              row.status = row.status == "1" ? "0" : "1";
            });
        })
        .catch(() => {
          row.status = row.status == "1" ? "0" : "1";
        });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType

      return new Promise(async (resolve) => {
        const res = await api[crudOperationType](params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    async deleteRowHandler(row) {
      try {
        await this.$confirm("是否确认删除该物料?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        await api.del(row.id);
        this.loadData();
        this.$modal.msgSuccess("删除成功");
      } catch (error) {
        console.log("取消删除");
      }
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleReset() {
      this.params = {};
      this.handleQuery();
    },
    handleExport() {
      const params = {
        ...this.params,
      };
      this.handleCommonExport(api.export, params);
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions, "deptOptions");
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    //去除children为空的children字段
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
    cleanRegionTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanRegionTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = res.data;
      });
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  padding: 20px;
  background: #fff;
  height: 100%;
}
.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}

.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
</style>
