<!-- 物料管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="rowAdd"
          v-has-permi="['materielManage:materiel:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['materielManage:materiel:batchImport']"
          >批量导入</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:materiel:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template #statusChange="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="0"
          :inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['materielManage:materiel:status'])"
        >
        </el-switch>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.5"
      maxSizeText="500m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage";
import Timeline from "@/components/Timeline/index.vue";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { initParams } from "@/utils/buse.js";
import { queryLog } from "@/api/common.js";

export default {
  name: "materialPage",
  components: { Timeline, BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      operationType: "add",
      uploadObj: {
        api: "/wlMaterialInfo/import",
        url: "/charging-maintenance-ui/static/物料导入模板.xlsx",
        extraData: {},
      },
      materielTypeOptions: [],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
        },
        {
          field: "materialClass",
          title: "物料分类",
          width: 120,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
        },
        {
          field: "materialParty",
          title: "是否甲供物料",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue === "T" ? "甲供物料" : "非甲供物料";
          },
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
        {
          field: "createName",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateName",
          title: "修改人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      recordList: [],
      unitOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: {
              placeholder: "请输入物料名称",
            },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: {
              placeholder: "请输入物料编码",
            },
          },
          {
            field: "materialClass",
            element: "el-select",
            title: "物料分类",
            props: {
              options: this.materielTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择物料分类",
              filterable: true,
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: 1 },
                { label: "启用", value: 0 },
              ],
              placeholder: "请选择状态",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增物料",
        editTitle: "编辑物料",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:materiel:edit"]),
        delBtn: checkPermission(["materielManage:materiel:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        formConfig: [
          {
            field: "materialClass",
            title: "物料分类",
            element: "el-autocomplete",
            attrs: {
              placeholder: "请输入物料分类",
              maxlength: 100,
              showWordLimit: true,
            },
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "materialClass");
              },
            },
            rules: [
              { required: true, message: "请输入物料分类" },
              { max: 100, message: "物料分类不能超过100个字符" },
            ],
          },
          {
            field: "materialNo",
            title: "物料编码",
            element: "el-input",
            attrs: {
              placeholder: "不填写的话，系统将自动生成",
              maxlength: 100,
              showWordLimit: true,
            },
            rules: [
              { max: 100, message: "物料编码不能超过100个字符" },
              {
                validator: this.validateMaterialCode,
                trigger: "blur",
              },
            ],
          },
          {
            field: "materialName",
            title: "物料名称",
            element: "el-autocomplete",
            attrs: {
              placeholder: "请输入物料名称",
              maxlength: 100,
              showWordLimit: true,
            },
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "materialName");
              },
            },
            rules: [
              { required: true, message: "请输入物料名称" },
              { max: 100, message: "物料名称不能超过100个字符" },
            ],
          },
          {
            field: "materialModel",
            title: "规格型号",
            element: "el-autocomplete",
            attrs: {
              placeholder: "请输入规格型号",
              maxlength: 100,
              showWordLimit: true,
            },
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "materialModel");
              },
            },
            rules: [{ max: 100, message: "规格型号不能超过100个字符" }],
          },
          {
            field: "materialFactory",
            title: "生产厂家",
            element: "el-autocomplete",
            attrs: {
              placeholder: "请输入生产厂家",
              maxlength: 100,
              showWordLimit: true,
            },
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "materialFactory");
              },
            },
            rules: [{ max: 100, message: "生产厂家不能超过100个字符" }],
          },
          {
            field: "unit",
            title: "单位",
            element: "el-select",
            props: {
              options: this.unitOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择单位",
            },
            rules: [{ required: true, message: "请选择单位" }],
          },
          {
            field: "unitPrice",
            title: "单价",
            element: "el-input-number",
            props: {
              controlsPosition: "right",
              min: 0,
              max: 999999,
              precision: 2,
              placeholder: "请输入单价",
            },
            slots: {
              append: "元",
            },
            rules: [
              {
                type: "number",
                min: 0,
                max: 999999,
                message: "单价范围为0-999999",
              },
            ],
          },
          {
            field: "materialParty",
            title: "是否甲供物料",
            element: "el-radio-group",
            props: {
              options: [
                { value: "T", label: "甲供物料" },
                { value: "F", label: "非甲供物料" },
              ],
            },
            rules: [{ required: true, message: "请选择是否甲供物料" }],
            defaultValue: "T",
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["materielManage:materiel:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.getMaterielTypeList();
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },
  methods: {
    checkPermission,
    getMaterielTypeList() {
      api
        .getHistoryType({
          type: "materialClass",
        })
        .then((res) => {
          this.materielTypeOptions = res.data?.map((x) => {
            return { dictValue: x, dictLabel: x };
          });
        });
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    querySearch(queryString, cb, apiName) {
      api
        .getHistoryType({
          type: apiName,
          [apiName]: queryString,
        })
        .then((res) => {
          const result =
            res.data?.map((item) => {
              return {
                value:
                  typeof item === "string" ? item : item.name || item.value,
              };
            }) || [];
          cb(result);
        })
        .catch(() => {
          cb([]);
        });
    },
    // 物料编码验证
    async validateMaterialCode(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      // 检查格式：支持中英文、数字
      const pattern = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
      if (!pattern.test(value)) {
        callback(new Error("物料编码只能包含中英文和数字"));
        return;
      }

      try {
        // 获取当前编辑的物料ID
        const currentId = this.$refs.crud?.getFormFields()?.materialId;
        const res = await api.checkCodeUnique({
          materialNo: value,
          materialId: currentId,
          type: this.operationType === "update" ? "UPDATE" : "INSERT",
        });
        if (res.data == "T") {
          this.$message.warning("物料编码不能重复");
          callback(new Error("物料编码不能重复"));
        } else {
          callback();
        }
      } catch (error) {
        callback(new Error("编码验证失败，请重试"));
      }
    },
    // 生成物料编码
    async generateMaterialCode(materialClass) {
      try {
        // 优先使用后端生成的编码
        const res = await api.generateMaterialNo();
        if (res.success && res.data) {
          return res.data;
        }
      } catch (error) {
        console.warn("后端生成物料编码失败，使用前端生成", error);
      }

      // // 后端失败时使用前端生成逻辑
      // if (!materialClass) return "";

      // // 获取物料分类首字母大写
      // const firstChar = materialClass.charAt(0).toUpperCase();
      // // 生成5位随机数
      // const randomNum = Math.floor(Math.random() * 100000)
      //   .toString()
      //   .padStart(5, "0");

      // return firstChar + randomNum;
    },
    handleLog(row) {
      // 注意：这里可能需要根据实际的日志接口来调整
      // 目前YApi中没有看到日志相关接口，暂时保留原有逻辑
      queryLog({ businessId: row.materialNo }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    async loadData() {
      this.loading = true;
      try {
        const res = await api.getTableData({
          ...this.params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data;
        this.tablePage.total = res.total;
      } finally {
        this.loading = false;
      }
    },
    async handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";

      // 停用状态需要特殊确认
      let confirmMessage = `是否确认${text}？`;
      if (row.status == "1") {
        confirmMessage = `确认停用该物料吗？\n\n注意：物料停用后将无法进行入库操作！`;
      }

      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          api
            .changeStatus({ materialId: row.materialId, status: row.status })
            .then((res) => {
              if (res.success) {
                this.$message.success(text + "成功");
                this.loadData();
              } else {
                row.status = row.status == "1" ? "0" : "1";
                this.$message.error(res.message || `${text}失败`);
              }
            })
            .catch((error) => {
              row.status = row.status == "1" ? "0" : "1";
              this.$message.error(
                `${text}失败：${error.message || "网络错误"}`
              );
            });
        })
        .catch(() => {
          row.status = row.status == "1" ? "0" : "1";
        });
    },
    rowAdd() {
      this.operationType = "add";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
      });
    },
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = {
        ...formParams,
      };

      // 新增时如果没有物料编码，自动生成
      if (
        crudOperationType === "add" &&
        !params.materialNo &&
        params.materialClass
      ) {
        params.materialNo = await this.generateMaterialCode(
          params.materialClass
        );
      }

      if (crudOperationType === "add") {
        await api.add(params);
      } else if (crudOperationType === "update") {
        await api.update(params);
      }

      this.loadData();
      this.$message.success(
        `${crudOperationType === "add" ? "新增" : "修改"}成功`
      );
    },
    async deleteRowHandler(row) {
      try {
        await this.$confirm(
          `确认删除物料"${row.materialName}"吗？\n\n删除后无法恢复！`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }
        );

        // 根据YApi接口文档，删除接口需要传递完整的物料对象
        await api.del(row);
        this.loadData();
        this.$message.success("删除成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`删除失败：${error.message || "网络错误"}`);
        }
      }
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.handleQuery();
    },
    handleExport() {
      const params = {
        ...this.params,
      };
      this.handleCommonExport(api.export, params);
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  padding: 20px;
  background: #fff;
  height: 100%;
}
.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}

.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
</style>
