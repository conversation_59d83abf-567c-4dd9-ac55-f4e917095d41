import request from "@/utils/request";

export default {
  // 分页查询入库信息
  getTableData(data) {
    return request({
      url: "/wlInBound/queryBoundByPage",
      method: "post",
      data: {
        ...data,
        operType: "in", // 固定为入库类型
      },
    });
  },

  // 批量入库
  batchInsert(data) {
    return request({
      url: "/wlInBound/batchInsert",
      method: "post",
      data: data,
    });
  },

  // 修改入库信息
  update(data) {
    return request({
      url: "/wlInBound/update",
      method: "post",
      data: data,
    });
  },

  // 删除入库信息
  remove(data) {
    return request({
      url: "/wlInBound/remove",
      method: "post",
      data: data,
    });
  },

  // 导出入库信息
  export(data) {
    return request({
      url: "/wlInBound/export",
      method: "post",
      data: {
        ...data,
        operType: "in", // 固定为入库类型
        quantityType: "F", // 入库首页导出
      },
    });
  },

  // 导入入库信息
  import(file) {
    const formData = new FormData();
    formData.append("file", file);
    return request({
      url: "/wlInBound/import",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // 根据物料编码查询物料信息（用于联动）
  getMaterialByCode(materialNo) {
    return request({
      url: "/wlMaterialInfo/getByMaterialNo",
      method: "post",
      data: { materialNo },
    });
  },

  // 查询物料编码列表（用于下拉选择）
  queryMaterialCodes(params) {
    return request({
      url: "/wlMaterialInfo/queryByPage",
      method: "post",
      data: {
        pageNum: 1,
        pageSize: 100,
        status: 0, // 只查询启用状态的物料
        ...params,
      },
    });
  },

  // 查询仓库列表
  queryWarehouseList(params) {
    return request({
      url: "/wlRepositoryInfo/getRepository",
      method: "post",
      data: params,
    });
  },
};
