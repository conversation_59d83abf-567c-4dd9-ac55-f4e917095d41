import request from "@/utils/request";

export default {
  // 调拨分页查询
  getTableData(data) {
    return request({
      url: "/wlInBound/queryBoundByPage",
      method: "post",
      data: {
        ...data,
        operType: "allot", // 固定为调拨类型
      },
    });
  },

  // 批量调拨
  batchInsert(data) {
    return request({
      url: "/wlAllotBound/batchInsert",
      method: "post",
      data: data,
    });
  },

  // 修改调拨
  update(data) {
    return request({
      url: "/wlAllotBound/update",
      method: "post",
      data: data,
    });
  },

  // 删除调拨
  remove(data) {
    return request({
      url: "/wlAllotBound/remove",
      method: "post",
      data: data,
    });
  },

  // 审核调拨
  audit(data) {
    return request({
      url: "/wlAllotBound/approve",
      method: "post",
      data: data,
    });
  },

  // 确认收货
  confirmReceive(data) {
    return request({
      url: "/wlAllotBound/confirm",
      method: "post",
      data: data,
    });
  },

  // 导出调拨
  export(data) {
    return request({
      url: "/wlInBound/export",
      method: "post",
      data: {
        ...data,
        operType: "allot", // 固定为调拨类型
        quantityType: "F", // 调拨首页导出
      },
    });
  },

  // 分页查询库存物料信息（用于选择调拨物料）
  queryMaterialByPage(data) {
    return request({
      url: "/wlOutBound/queryMaterialByPage",
      method: "post",
      data: data,
    });
  },

  // 查询仓库列表
  queryWarehouseList(params) {
    return request({
      url: "/wlRepositoryInfo/getRepository",
      method: "post",
      data: params,
    });
  },

  // 查询省市树形数据
  queryCityTree(params) {
    return request({
      url: "/system/area/tree",
      method: "get",
      params: params,
    });
  },

  // 根据调拨单号查询该单号下的所有物料记录
  getAllocateDetailsByOperNo(operNo) {
    return request({
      url: "/wlInBound/queryBoundByPage",
      method: "post",
      data: {
        operNo: operNo,
        operType: "allot",
        pageNum: 1,
        pageSize: 1000, // 获取所有记录
      },
    });
  },

  // 查询系统用户列表（用于选择审批人）
  queryUserList(params) {
    return request({
      url: "/system/user/list",
      method: "get",
      params: {
        pageNum: 1,
        pageSize: 100,
        status: "0", // 只查询正常状态的用户
        ...params,
      },
    });
  },
};
