import request from "@/utils/request";

export default {
  // 分页查询出库信息
  getTableData(data) {
    return request({
      url: "/wlInBound/queryBoundByPage",
      method: "post",
      data: {
        ...data,
        operType: "out", // 固定为出库类型
      },
    });
  },

  // 分页查询库存物料信息（用于选择出库物料）
  queryMaterialByPage(data) {
    return request({
      url: "/wlOutBound/queryMaterialByPage",
      method: "post",
      data: data,
    });
  },

  // 批量出库
  batchInsert(data) {
    return request({
      url: "/wlOutBound/batchInsert",
      method: "post",
      data: data,
    });
  },

  // 修改出库信息
  update(data) {
    return request({
      url: "/wlOutBound/update",
      method: "post",
      data: data,
    });
  },

  // 删除出库信息
  remove(data) {
    return request({
      url: "/wlOutBound/remove",
      method: "post",
      data: data,
    });
  },

  // 确认收货
  confirm(data) {
    return request({
      url: "/wlOutBound/confirm",
      method: "post",
      data: data,
    });
  },

  // 导出出库信息
  export(data) {
    return request({
      url: "/wlInBound/export",
      method: "post",
      data: {
        ...data,
        operType: "out", // 固定为出库类型
        quantityType: "F", // 出库首页导出
      },
    });
  },

  // 查询仓库列表
  queryWarehouseList(params) {
    return request({
      url: "/wlRepositoryInfo/getRepository",
      method: "post",
      data: params,
    });
  },

  // 查询省市树形数据
  queryCityTree(params) {
    return request({
      url: "/system/area/tree",
      method: "get",
      params: params,
    });
  },

  // 根据出库单号查询该单号下的所有物料记录
  getOutboundDetailsByOperNo(operNo) {
    return request({
      url: "/wlInBound/queryBoundByPage",
      method: "post",
      data: {
        operNo: operNo,
        operType: "out",
        pageNum: 1,
        pageSize: 1000, // 获取所有记录
      },
    });
  },
};
