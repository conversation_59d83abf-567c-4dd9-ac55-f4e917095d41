import request from "@/utils/request";

export default {
  // 分页查询物料信息
  getTableData(data) {
    return request({
      url: "/wlMaterialInfo/queryByPage",
      method: "post",
      data: data,
    });
  },

  // 新增物料
  add(data) {
    return request({
      url: "/wlMaterialInfo/save",
      method: "post",
      data: data,
    });
  },

  // 更新物料
  update(data) {
    return request({
      url: "/wlMaterialInfo/update",
      method: "post",
      data: data,
    });
  },

  // 删除物料
  del(data) {
    return request({
      url: "/wlMaterialInfo/remove",
      method: "post",
      data: data,
    });
  },

  // 物料编码唯一性校验
  checkCodeUnique(data) {
    return request({
      url: "/wlMaterialInfo/checkMaterialNo",
      method: "post",
      data: data,
    });
  },

  // 生成物料编号
  generateMaterialNo() {
    return request({
      url: "/wlMaterialInfo/generateMaterialNo",
      method: "post",
    });
  },

  // 根据物料编号查询物料信息
  getByMaterialNo(materialNo) {
    return request({
      url: "/wlMaterialInfo/getByMaterialNo",
      method: "post",
      data: { materialNo },
    });
  },

  // 状态切换
  changeStatus(data) {
    return request({
      url: "/wlMaterialInfo/changeStatus",
      method: "post",
      data: data,
    });
  },

  // 导出
  export(data) {
    return request({
      url: "/wlMaterialInfo/export",
      method: "post",
      data: data,
    });
  },

  // 导入
  import(file) {
    const formData = new FormData();
    formData.append("file", file);
    return request({
      url: "/wlMaterialInfo/import",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // 获取历史类型（用于自动完成）
  getHistoryType(data) {
    return request({
      url: "/wlMaterialInfo/getHistoryType",
      method: "post",
      data: data,
    });
  },
};
